'use server'

import { Config } from '../../../bot/config/config'
import { PromptBuilder } from '../../../bot/service/moer/components/agent/context'
import { ChatHistoryWithRoleAndDate, ExtractUserSlotsV2, ILogInfo, UserSlots } from '../../../bot/service/moer/components/flow/helper/slotsExtract'
import { contentWithFrequency } from '../../../bot/service/moer/storage/chat_state_store'


export async function extractUserSlots({ previousUserSlotsRecord, chatHistory, logInfo, model }:{
    previousUserSlotsRecord: Record<string, contentWithFrequency>,
    chatHistory: ChatHistoryWithRoleAndDate [],
    logInfo?: ILogInfo
    model?:string
}):Promise<{ userslots: Record<string, contentWithFrequency>; thisRoundExtract: Record<string, contentWithFrequency> }> {
  'use server'
  Config.setting.langsmith.projectName = 'evaluators'
  Config.setting.localTest = false
  const bootCampBasicSetting = PromptBuilder.getBootCampBasic()
  const previousUserSlots = UserSlots.fromRecord(previousUserSlotsRecord)
  const previousTopicAndSubTopicPrompt = ExtractUserSlotsV2.topicAndSubTopicIntoPrompt(previousUserSlots.getTopicAndSubTopic())
  const stagePrompt = ''
  const currentUserSLots = await ExtractUserSlotsV2.rawExtract({ model, chatHistory, bootCampBasicSetting, stagePrompt, previousTopicAndSubTopicPrompt, logInfo })
  await previousUserSlots.merge(currentUserSLots, logInfo)
  return { userslots:previousUserSlots.toRecord(), thisRoundExtract:currentUserSLots.toRecord() }
}
