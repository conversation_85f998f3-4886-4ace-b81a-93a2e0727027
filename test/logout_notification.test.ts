import { PromptBuilder } from '../bot/service/moer/components/agent/context'
import { LLM } from '../bot/lib/ai/llm/LLM'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    await ChatStatStoreManager.initState('7881300846030208_1688854546332791')
    const userSlotsPainPoints = PromptBuilder.getUserSlotsText('7881300846030208_1688854546332791')

    // 根据天数，返回对应信息
    const courseContentSummary = {
      1: '第一课：情绪减压，解析情绪和睡眠问题的成因和处理方式，再从手把手教冥想入门姿势。最带练对情绪和睡眠非常有帮助冥想《沉浸式秒睡》',
      2: '第二课：财富唤醒，聚焦【财富问题】，解读什么是富足和吸引力法则，帮助大家一步步找到自己负债、迷茫的内在原因。通过【财富果园】的冥想带练，帮助大家扫除对于物质、财富的内心阻碍，养成更加容易获取财富、打理财富的思维习惯',
      3: '第三课：红靴子是老师最重磅的课程，老师会带练【红靴子飞跃】冥想，主要聚焦于大家专注力、内在能量的提升。老师说如何一生只练一个冥想，就练习红靴子吧。这堂课结束的时候来时会和想精进学习的人说明21天的系统课内容',
      4: '第三课：唐宁老师会传授只有线下才会开设的蓝鹰预演，帮助我们更好地掌控自己的情绪和思维，认识自己、明确目标，并激发内在的力量，提高振动频率，以实现梦想，也能更从容地应对新挑战与困难。同时还会学习无限数息法延长呼吸，如何深呼吸延长几倍',
    }

    // 不同天数，返回不同的话术，避免重复
    const logOutNotificationPrefix = {
      1: '同学，这边看到你掉线了，有什么问题么?',
      2: 'Hi, 刚刚看到不在直播间了。需要帮助吗？',
      3: '同学，看到掉线了，是网络出问题了吗?',
      4: '同学咱这边掉线了，是有什么问题吗?',
    }

    for (let i = 1; i <= 4 ; i++) {
      const llmRes = await LLM.predict(`结合客户的情况和今天冥想课程的内容，提醒掉线的客户回到直播课程。
如果相关性不强，可以多推理几步，找一下间接原因，从而结合上客户的情况。
      
客户信息：
{
    pain_points: "睡眠不好"
}

课程内容：
${courseContentSummary[i]}

例如：${logOutNotificationPrefix[i]} 今天老师会针对你提到过的 xxx 讲 xxx，希望你听完呢

以 "${logOutNotificationPrefix[i]}" 开始， "希望你听完呢" 结束，简洁清晰。`)

      console.log(llmRes)
    }

    // if (userSlotsPainPoints) {

    // }
  }, 60000)
})