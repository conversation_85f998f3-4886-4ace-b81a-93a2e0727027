import { PrismaMongoClient } from '../mongodb/prisma'
import logger from './logger'


export enum IEventType {
  TransferToManual = '转交人工',
  ManualReply = '人工回复',
  PreCourseComplete = '小课堂完课',
  CourseComplete = '正式课完课',
  liveStatusChange = '直播状态变动',
  EnergyTestScore = '能量测评分数',
  PaymentComplete = '完成支付',
  PaymentNotCompleted = '未完成支付',
  HomeworkComplete = '作业完成',
  NodeInvoke = '节点调用',
  LiveStreamWillingToBuy = '直播课意向购买',
  BootcampOrder = '入门营订单',
  BindWechat = '绑定微信',
  addWechatAPI = 'API加微',
  addWechatPhoneCall = '电话加微',
  addWechatManually = '人工加微',
  salesPaymentIntention = '销售支付意图',
  thinkStrategy = '思考策略',
  salesSendInvitation = '销售发送邀约',
  salesCase = '销售案例',
  sceneExperience = '场景经验'
}
/**
 * 埋点记录
 */
export class EventTracker {
  public static track(chat_id: string, event: IEventType | string, meta?: Record<string, any>) {
    if (!meta) {
      meta = {}
    }

    // 异步记录
    PrismaMongoClient.getInstance().event_track.create({
      data: {
        timestamp: new Date(),
        chat_id,
        type: event,
        meta
      }
    }).catch((e) => {
      logger.error('埋点失败', e)
    })
  }
}