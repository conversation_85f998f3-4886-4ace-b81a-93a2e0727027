import { BaseMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { Config } from '../../../config/config'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { IterableReadableStream } from '@langchain/core/dist/utils/stream'
import { AzureOpenAIClient, CheapOpenAI, OpenAIClient, QwenMax } from './client'
import { z } from 'zod'
import { Runnable, RunnableConfig } from '@langchain/core/runnables'
import logger from '../../../model/logger/logger'
import { StringHelper } from '../../string'
import { ChatPromptTemplate, ParamsFromFString, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { randomSleep } from '../../schedule/schedule'
import { MessageSender } from '../../../service/moer/components/message/message_send'
import { IWecomMsgType } from '../../juzi/type'
import { PathHelper } from '../../path'
import { catchError } from '../../error/catchError'
import { FileHelper } from '../../file'

type ZodFunctionDef<Parameters = any> = {
    /** Function name. */
    name: string
    /** A description of what the function does. */
    description: string
    /** Zod schema defining the function's parameters, to convert to JSON schema. */
    schema: z.ZodType<Parameters>
};

interface LLMInitParams {
  model?: string
  temperature?: number
  max_tokens?: number
  systemPrompt?: string
  runnableConfig?: RunnableConfig
  meta?: Record<string, any> // 用于 langSmith 记录元信息，方便查询，round_id 对应了 run_id， chat_id 对应了 thread_id
  projectName?: string // 用于配置 langSmith 日志记录到的项目
}

interface IBaseMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

enum ClientType {
  AZURE = 'azure',
  OPENAI = 'openai',
  CHEAP = 'cheap',
  QWEN = 'qwen'
}


/**
 * OpenAI API 封装
 * 注意：
 * 1. 聊天记录需自己维护
 * 2. 尽量不要引入 openai 包，所有功能尽量以 LangChain 实现，保证稳定性
 */
export class LLM {
  private readonly systemPrompt: string
  private readonly model: string
  private readonly temperature: number
  private readonly max_tokens: number
  private runnableConfig: RunnableConfig
  private runId: string // 必须是 UUID v4 格式

  constructor(params?: LLMInitParams) {
    // 设置 LangSmith
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
    if (params?.projectName) {
      process.env.LANGCHAIN_PROJECT = params.projectName
    }

    if (!params) {
      params = {}
    }

    if (!params.model) {
      params.model = 'gpt-4.1'
    }
    this.model = params.model

    if (!params.temperature) {
      params.temperature = 0
    }
    this.temperature = params.temperature

    if (!params.max_tokens) {
      params.max_tokens = 1000
    }
    this.max_tokens = params.max_tokens

    if (params.systemPrompt) {
      this.systemPrompt = params.systemPrompt
    }

    if (params.runnableConfig) {
      this.runnableConfig = params.runnableConfig
    } else {
      this.runnableConfig = {}
    }

    if (params.meta) {
      this.runnableConfig.metadata = params.meta
      if (params.meta.round_id) {
        this.runId = params.meta.round_id
      }

      if (params.meta.name) {
        params.meta.promptName = params.meta.name
      }

      if (params.meta.chat_id) {
        params.meta.thread_id = params.meta.chat_id // 添加 thread_id 方便查询
      }
    }
  }

  private createClient(type: ClientType) {
    switch (type) {
      case ClientType.AZURE:
        return AzureOpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature,
          max_tokens: this.max_tokens
        })
      case ClientType.OPENAI:
        return OpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature
        })
      case ClientType.CHEAP:
        return CheapOpenAI.getClient(this.model, this.temperature)
      case ClientType.QWEN:
        return QwenMax.getClient(this.temperature)
      default:
        console.warn('Unknown client type:', type)
        // 保底按照 Azure OpenAI
        return AzureOpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature,
          max_tokens: this.max_tokens
        })
    }
  }

  /**
   * 根据 model 参数调整调用优先级
   * @private
   */
  // 定义客户端类型枚举

  private getClients() {
    // 根据条件确定客户端顺序
    let clientOrder: ClientType[]

    if (this.model.startsWith('gpt-4.1') || this.model === 'gpt-4.1-mini') {
      clientOrder = [ClientType.AZURE, ClientType.CHEAP, ClientType.OPENAI, ClientType.QWEN]
    } else if (this.model.startsWith('claude')) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.OPENAI, ClientType.QWEN]
    } else if (this.model.startsWith('qwen-max')) {
      clientOrder = [ClientType.QWEN, ClientType.AZURE, ClientType.OPENAI, ClientType.CHEAP]
    } else if (Config.setting.localTest) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.OPENAI, ClientType.QWEN]
    } else { // 保底配置
      clientOrder = [ClientType.AZURE, ClientType.OPENAI, ClientType.CHEAP, ClientType.QWEN]
    }

    // 按确定的顺序返回客户端
    return clientOrder.map(this.createClient.bind(this))
  }

  /**
   * 使用文本或 PromptTemplate 调用 LLM
   * LLM.predict('Hello, World!')
   * LLM.predict(PromptTemplate.from('Hello, {name}!'), { name: 'World' })
   * @param text 文本 或 PromptTemplate, 如果是 PromptTemplate，则使用 params 参数替换模板中的变量
   * @param promptParams
   */
  async predict<T extends string>(
    text: string | SystemMessagePromptTemplate<ParamsFromFString<T>> | Runnable,
    promptParams?: ParamsFromFString<T>
  ): Promise<string> {
    const clients = this.getClients()
    const parser = new StringOutputParser()

    for (const client of clients) {
      try {
        if (typeof text === 'string') {
          return await client.pipe(parser).withConfig(this.runnableConfig).invoke(text, { runId: this.runId })
        } else if (promptParams) {
          return await text.pipe(client).pipe(parser).withConfig(this.runnableConfig).invoke(promptParams, { runId: this.runId })
        }
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 简单使用单条文本调用 OpenAI
   */
  public static async predict<T extends string>(
    text: string | SystemMessagePromptTemplate<ParamsFromFString<T>> | Runnable,
    params?: LLMInitParams,
    promptParams?: ParamsFromFString<T>
  ): Promise<string> {
    const llm = new LLM(params)
    return await llm.predict(text, promptParams) as string
  }

  // /**
  //  * 构建一个稳健，简洁的 tool calling 调用
  //  * TODO 暂时只支持单 tool 调用
  //  * @param functionDefinition
  //  * @param query
  //  */
  // async fc_call(functionDefinition: ZodFunctionDef | ZodFunctionDef[], query: string) {
  //   if (!Array.isArray(functionDefinition)) {
  //     functionDefinition = [functionDefinition] as ZodFunctionDef[]
  //   }
  //
  //   const tools = functionDefinition.map((tool) => new DynamicStructuredTool({
  //     name: tool.name,
  //     description: tool.description,
  //     schema: tool.schema,
  //     func: async (params) => '',
  //   }))
  //
  //   // 先使用 fc_call 尝试，不行的话降级为 Prompt 模式调用
  //   try {
  //     const response = await Retry.retry(2, async () => {
  //       const clients =  [OpenAIClient.getClient(), AzureOpenAIClient.getClient()].map((client) => client.bindTools(tools))
  //
  //       for (const client of clients) {
  //         try {
  //           return await client.invoke(query)
  //         } catch (e) {
  //           logger.error('Error using client fc Call', clients.indexOf(client), e)
  //         }
  //       }
  //
  //       throw new Error('All clients fc call failed')
  //     })
  //
  //     if (!response.tool_calls) {
  //       throw new Error('No tool calls found in response')
  //     }
  //
  //     if (response.tool_calls.length >= 1) {
  //       return response.tool_calls[0].args
  //     }
  //   } catch (e) {
  //     console.error(e)
  //
  //     // 降级为 Prompt 模式调用
  //     console.warn('Function call retry with GPT4 prompt')
  //
  //     return await Retry.retry(2, async () => {
  //       // TODO 先只支持单 tool 调用
  //       if ((functionDefinition as ZodFunctionDef[]).length != 1) {
  //         throw new Error('Only support single tool call')
  //       }
  //
  //       const jsonRes = await AzureOpenAIClient.getClient().predict(`<Instructions>
  //   You are tasked with converting a user query to JSON format.
  //
  //   <Input>
  //   <user_query>
  //   ${query}
  //   </user_query>
  //   </Input>
  //
  //   Here's how you should approach this task:
  //
  //   1. Read the user's query carefully. Focus on extracting information related to the fields described as follows:
  //   ${functionDefinition[0].description}
  //   ${JSON.stringify(toJsonSchema(functionDefinition[0] as any).parameters, null, 2)}
  //
  //   2. Map the extracted information to the corresponding fields. Pay special attention to the data types and units (paying close attention to the allowed enum values).
  //
  //   3. Construct the JSON. Start with an opening curly brace, include each field as a key with the extracted value as the value, and separate multiple fields with commas. Close with a closing curly brace. Put the JSON inside <JSON> tag.
  //
  //   4. Ensure that the JSON is correctly formatted with proper keys, values, and data types as specified in the field descriptions.
  //
  //
  //   Follow these steps to convert the user query you receive into the appropriate JSON format. Think step by steps. Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.
  //   </Instructions>`)
  //
  //       // regex 提取出 JSON 部分
  //       const jsonStr = JSONHelper.extractJSONPart(jsonRes)
  //
  //       if (!jsonStr) {
  //         throw new Error('Failed to extract JSON part from GPT4 prompt')
  //       }
  //
  //       return JSONHelper.parse(jsonStr)
  //     })
  //   }
  // }


  private isMessagesContainSystemPrompt(messages: BaseMessage[]): boolean {
    return messages.length > 0 && messages[0]._getType() === 'system'
  }

  /**
   * 使用一组消息或 ChatPromptTemplate 调用 LLM，需要自己维护聊天记录
   * const messages = [
   *   new SystemMessage("You are a helpful assistant"),
   *   new HumanMessage("Tell me a joke about {topic}"),
   * ]
   * LLM.predictMessage(messages)
   *
   * const promptTemplate = ChatPromptTemplate.fromMessages([
   *   ["system", "You are a helpful assistant"],
   *   ["user", "Tell me a joke about {topic}"],
   * ])
   * LLM.predictMessage(promptTemplate, {topic: 'fuck'})
   * @param messages
   * @param params
   */
  async predictMessage<T extends BaseMessage [] | ChatPromptTemplate<any>>(messages: T, params?: T extends ChatPromptTemplate<infer P> ? P : never): Promise<string> {
    const chatHistory = messages

    const parser = new StringOutputParser()

    const clients =  this.getClients()
    for (const client of clients) {
      try {
        if (Array.isArray(chatHistory)) {
          return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
        } else {
          return await (messages as ChatPromptTemplate).pipe(client).pipe(parser).withConfig(this.runnableConfig).invoke(params, { runId: this.runId })
        }
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 使用一组消息调用 OpenAI，需要自己维护聊天记录
   * @param messages
   * @param params
   */
  public static async predictMessage(messages: BaseMessage[], params?: LLMInitParams): Promise<string> {
    const llm = new LLM(params)
    return await llm.predictMessage(messages) as string
  }

  /**
   * 流式输出，返回一个可迭代的流，包含多个 string 输出
   * 例如：
   * for await (const chunk of stream) {
   *   console.log(chunk);
   * }
   *
   * 输出：
   *   Hello
   *   !
   *   How
   *   can
   *   I
   *   assist
   *   you
   *   today
   *   ?
   *   参考： https://js.langchain.com/docs/modules/model_io/models/chat/how_to/streaming
   */
  async stream(messages: BaseMessage[], systemPrompt?: string): Promise<IterableReadableStream<string>> {
    let chatHistory = this.cleanPromptMessages(messages)
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...messages]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(messages)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...messages]
    }
    const parser = new StringOutputParser()

    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).stream(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)
      }
    }

    throw new Error('All attempts to stream with available clients have failed.')
  }

  cleanPromptMessage(prompt: string) {
    return StringHelper.replaceMultipleBlankLines(prompt).trim()
  }

  async imageChat(imageUrl: string, systemPrompt?: string) {
    const message = new HumanMessage({
      content: [
        { type: 'image_url', image_url: { url: imageUrl } }
      ]
    })
    let chatHistory: BaseMessage[] = [message]
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...chatHistory]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(chatHistory)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...chatHistory]
    }
    const parser = new StringOutputParser()
    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error summarizing image:', e)
        throw new Error('Failed to summarize image.')
      }
    }
  }

  /**
   * Prompt 格式化，移除无用空行
   * @param messages
   * @private
   */
  private cleanPromptMessages(messages: BaseMessage[]) {
    messages.forEach((message) => {
      if (typeof message.content === 'string') {
        message.content = this.cleanPromptMessage(message.content)
      }
    })

    return messages
  }

  static async sendMsg(line: string, chat_id: string, user_id: string, round_id: string, noSplit?: boolean) {
    if (noSplit) {
      // 直接把文件移除掉，进行输出
      line = line.replaceAll(/\[[^\]]*]/g, '')

      return await MessageSender.sendById ({
        chat_id: chat_id,
        user_id: user_id,
        ai_msg: line
      }, { round_id: round_id })
    } else {
      // 将文件部分提取出来，放到文本后面。
      const parts = line.split(/\[((?:.*?)_(?:\w{4})\.(?:\w+))\]/)
      const fileRegex =   /.*?_\w{4}\.\w+/

      let textPart = ''
      const filePart: string[] = []

      for (let part of parts) {
        if (fileRegex.test(part)) {
          filePart.push(part)
        } else {
          if (part.endsWith('：') || part.endsWith(':')) {
            part = part.replace(/[：:]/, '')
          }

          textPart += part
        }
      }

      textPart = textPart.replaceAll(/\s+/g, ' ').replaceAll(/\[[^\]]*]/g, '').trim()

      if (textPart) {
        await MessageSender.sendById ({
          chat_id: chat_id,
          user_id: user_id,
          ai_msg: textPart
        }, { round_id: round_id })
      }

      for (const fileString of filePart) {
        // 这是一个文件名
        const description = fileString.split('_')[0]
        const extension = PathHelper.getFileExt(fileString)
        const fileName = fileString

        // 构建消息对象
        let message

        const fileUrl = `https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/rag_file/${encodeURIComponent(fileName)}`
        const [err, res]  = await catchError(FileHelper.getFileSizeFromUrl(fileUrl))
        if (err || (res && res === 0)) {
          // 文件不存在，有可能是模型幻觉，或者文件被删除了
          logger.error(`${fileName}文件不存在`)
          continue
        }

        // 根据文件后缀名判断文件类型
        switch (extension.toLowerCase ()) {
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'webp':
            message = {
              type: IWecomMsgType.Image,
              url: fileUrl
            }
            break
          case 'mp4':
          case 'avi':
          case 'mov':
          case 'wmv':
          case 'flv':
          case 'mkv':
            message = {
              type: IWecomMsgType.Video,
              url: fileUrl
            }
            break
          default:
            message = {
              type: IWecomMsgType.File,
              name: description,
              url: fileUrl
            }
        }

        await randomSleep(3000, 5000)

        // 发送文件消息
        await MessageSender.sendById ({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: `[${description}]`,
          send_msg: message,
        }, {
          shortDes: `[${description}]`,
          round_id: round_id
        })
      }
    }

  }
}