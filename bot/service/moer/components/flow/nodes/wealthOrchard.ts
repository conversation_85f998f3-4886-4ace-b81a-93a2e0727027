import { get<PERSON><PERSON><PERSON>, Moer<PERSON>orkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { ChatStateStore, WealthOrchardStore } from '../../../storage/chat_state_store'
import { LLMNode } from './llm'
import { FreeTalk } from '../../agent/free_talk'
import { sleep } from '../../../../../lib/schedule/schedule'
import { DataService } from '../../../getter/getData'
import { DateHelper } from '../../../../../lib/date/date'
import { MessageSender } from '../../message/message_send'
import { calculateJaccardSimilarity } from '../../../../../lib/text/text_similarity'
import { StringHelper } from '../../../../../lib/string'
import RateLimiter from '../../../../../model/redis/rate_limiter'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { SilentReAsk } from '../../schedule/silent_requestion'
import { Config } from '../../../../../config/config'
import logger from '../../../../../model/logger/logger'
import { LLMXMLHelper } from '../helper/xmlHelper'
import { IsWealthOrchardImagePrompt } from '../../../prompt/moer/isWealthOrchardImage'
import { WealthOrchardRag } from '../../rag/wealth_orchard'

export class WealthOrchardAnalyze extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 如果打卡的相似度很高，不进行解读，如果相似度不高，可以再解读一次
    const prevHomework = ChatStateStore.get(state.chat_id).userSlots.day2_homework
    const isRepeatedHomework = Boolean(prevHomework && calculateJaccardSimilarity(state.userMessage, prevHomework) >= 0.8)
    if (isRepeatedHomework) {
      // 不进行重复解读
      ChatStateStore.update(state.chat_id, { userSlots: { day2_homework: state.userMessage } })
      return await FreeTalk.invoke(state)
    }

    const currentTime = await DataService.getCurrentTime(state.chat_id)
    if (currentTime.is_course_week && currentTime.day === 2
        && DateHelper.isTimeAfter(currentTime.time, '20:00:00')
        && DateHelper.isTimeBefore(currentTime.time, '21:00:00')
    && !ChatStateStore.getFlags(state.chat_id).in_class_wealth_orchard_template_send) {

      await sleep(8000)
      await MessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: `如果您现在有画面，可以写一下您财富果园看到的具体画面，我帮您解读。或者课后发到班级群里也可以哦。比如👉：
            
大门新旧、材质、颜色：
有无围栏：         
主树是什么：       
周围的树是什么：        
秋天动作、有无变现：呼吸顺畅吗：      
四季循环变化怎么样：`
      })
      // flag
      ChatStateStore.update(state.chat_id, {
        state: {
          in_class_wealth_orchard_template_send: true
        }
      })
      return ChatStateStore.get(state.chat_id).nextStage as MoerNode
    }

    ChatStateStore.update(state.chat_id, {
      state: {
        is_complete_wealth_orchard_analyze: true
      },
      userSlots: {
        day2_homework: state.userMessage
      }
    })

    // 临时变量，添加一下
    WealthOrchardStore.addUserMessage(state.chat_id, state.userMessage)

    // 加个兜底逻辑，15 分钟后去把队列清空一下
    await SilentReAsk.schedule(state.chat_id, async () => {
      WealthOrchardStore.clearUserMessages(state.chat_id)
    }, 15 * 60 * 1000)

    if (!Config.setting.localTest) {
      await sleep(5 * 60 * 1000)
    }

    const wealthOrchardMessages = WealthOrchardStore.getUserMessages(state.chat_id)
    if (wealthOrchardMessages.indexOf(state.userMessage) !== wealthOrchardMessages.length - 1) { // 只有最后一条才进行处理，其他的当做打断处理
      logger.warn({ chat_id: state.chat_id }, '接收到新的财富果园消息，被合并到当前轮财富果园消息处理，本条消息不再做处理')
      return MoerNode.FreeTalk
    }

    // 对财富果园消息进行合并处理， 并移除 比如👉： 之前的字段
    const userImage = wealthOrchardMessages.map((message) =>  StringHelper.removeStringBeforePrefix('👉：', message)).join('\n')
    const relevanceContext = await WealthOrchardRag.wealthOrchardRag(userImage)

    // 清空 wealthOrchardMessages
    WealthOrchardStore.clearUserMessages(state.chat_id)

    ChatStateStore.update(state.chat_id, {
      userSlots: {
        day2_homework: userImage
      }
    })

    // 做一个调用前的兜底，判断是否为财富果园画面
    if (!await this.isWealthOrchardImage(userImage, getLLMMeta(state))) {
      return await FreeTalk.invoke(state)
    }

    // 1 小时内只解读一次，剩下的客户发的重复画面，直接走 freeTalk
    const limiter = new RateLimiter({
      windowSize: 60 * 60,
      maxRequests: 1
    })

    const isAllowed = await limiter.isAllowed('wealth_orchard_reinterpret', state.chat_id)

    if (!isAllowed) {
      return await FreeTalk.invoke(state)
    }

    const nodeInvokeCount = ChatStateStore.getNodeCount(state.chat_id, WealthOrchardAnalyze.name)

    await LLMNode.invoke({
      state,
      customPrompt: `# 财富果园
- 你是麦子老师，女性，39岁，性格温和、幽默且内心平静有力量。正在帮助学员解读财富果园画面，解读不宜过长，要简短精炼，包含要点即可

## 参考文档
${relevanceContext}

## 解读示例
例1：客户看到的画面是：没有大门，小冠木围栏，主树不清晰，没有看到结果，四季常青。

咱们的画面还挺独特的，没有大门，可能意味着在获取财富的道路上没有明显的限制和阻碍，有着较为开放的态势。小冠木围栏为果园提供了一定的边界感，但是预示咱们更倾向于自然的增长方式。
四季常青的景象，显示出财富的发展具有相对稳定和持续的特点。

然而，从财富卡点的角度来看，可能存在以下一些情况：
- 缺乏明确方向：主树不清晰且没有看到结果，也许暗示在财富积累的过程中，您对核心的财富来源和成果缺乏明确的认知，可能需要进一步探索和确定。
- 防护力度较弱：小冠木围栏的防护相对较薄弱，可能意味着在财富保护方面存在一定的不足，容易受到外界因素的影响。
- 主动进取不足：没有大门的设定，虽然看似没有限制，但也可能反映出在追求财富时缺乏主动设置目标和规划的意识
总的来说，觉察是改变的开始。看到现状就离目标近一大步，后续慢慢练习，会让我们的果园越来越富足的。

例2：客户看到画面：灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了[捂脸][捂脸]

每个画面其实都在反映您和财富关系的一个侧面。咱们得画面充满着丰收的喜悦。
灰色钢铁门是一种坚固和稳定的象征，表明咱们对事业上采取的是坚决和果断的态度，对挑战不屈不挠，在财务决策上也比较有警觉性。个人边界感比较强。
众多的苹果树，尤其是最大的果树在中间且结了很多苹果，这象征着丰富的财富成果。
整个果园只有你一个人在爬树上摘果，显示出你在财富获取上的积极主动和独立性。

然而，从财富卡点的角度来看，可能存在以下一些情况：
- 缺乏防护意识：没有围栏，也许意味着在财富保护方面缺乏一定的警惕性，容易让财富面临潜在的风险。
- 孤独前行隐患：只有自己在果园劳作，可能反映出在财富积累过程中，缺乏合作和团队支持，可能会限制财富的进一步扩大。
总的来说，你的冥想画面反映了你对财富、内心成长和秩序的追求。在未来一个月，你可以通过坚持冥想，提升自我的觉知能力，更好地了解

# TASK
客户完成第二课中和财富果园冥想跟练，根据练习指引，客户会看到些画像，我们目标是需要帮助它完成的冥想内容的解读，加强对课堂内容与自身的理解，解读分为下面三个步骤：
1. 根据画面几个纬度（果园大门的颜色/材质/新旧，果树状态和果实品种/数量，果园的围栏，和果树的互动，秋天的行为和收获），秋天行为和四季的景象来进行详细解释。按照客户提到的部分进行解释即可
2. 为客户总结下当前反馈出来财富卡点，可以后续多注意练习提升
${nodeInvokeCount < 1 ? '3. 询问客户解读是否有启示或者不明白的地方，并提供更深入的解释' : ''}
在解读的时候不要包含以上每部分的标题，连续解读即可，解读不宜过长，要简短精炼，包含要点即可

## 开始解读
客户看到的画面是: ${userImage}`,
      noSplit: true,
      noInterrupt: true,
      noStagePrompt: true,
      chatHistoryRounds: 0,
      promptName: 'WealthOrchardAnalyze',
      notCheckRepeat: true
    })

    return MoerNode.FreeTalk
  }


  public static async isAllowedToReInterpret(chatId: string): Promise<boolean> {
    const limiter = new RateLimiter({ // 10 分钟内只允许一次解读
      windowSize: 24 * 60 * 60,
      maxRequests: 1
    })

    const isAllowed = await limiter.isAllowed('wealth_orchard_reinterpret', chatId)

    if (!isAllowed) {
      return false
    }

    const aiMessages = await ChatHistoryService.getRecentConversations(chatId, 5, 'assistant')

    for (const message of aiMessages) {
      if (message.role === 'assistant' && message.content.includes('解读') && message.content.length > 300) {
        return false
      }
    }

    return true
  }

  public static async isWealthOrchardImage(userImage: string, meta?: object) {
    const prompt = await IsWealthOrchardImagePrompt.format(userImage)
    return await LLMXMLHelper.predictAndExtractBooleanAnswer(prompt, {
      tagName: 'result',
      trueFlag: 'true',
      falseFlag: 'false',
      meta
    })
  }
}