import { MoerWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { ExtractUserSlots, ExtractUserSlotsV2 } from '../helper/slotsExtract'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { ObjectUtil } from '../../../../../lib/object'
import { ExtractUserSlotsPrompt } from '../../../prompt/moer/extractUserSlots'
import { LLMNode } from './llm'
import { SendEnergyTest } from '../schedule/task/sendEnergyTest'
import { TaskName } from '../schedule/type'
import { IsNeedEmpathyPrompt } from '../../../prompt/moer/isNeedEmpathy'
import { LLMXMLHelper } from '../helper/xmlHelper'
import { sleep } from '../../../../../lib/schedule/schedule'
import { MessageSender } from '../../message/message_send'
import { DataService } from '../../../getter/getData'
import { FreeTalk } from '../../agent/free_talk'
import { ChatInterruptHandler } from '../../message/interrupt_handler'
import dayjs from 'dayjs'
import { IWecomMsgType } from '../../../../../lib/juzi/type'

export enum MoerUserSlotType {
    is_attend_live_stream = 'live_class_confirmation',
    meditation_goal = 'meditation_goal',
}

/**
 * 挖需
 */
export class IntentionQueryNode extends MoerWorkFlowNode {
    @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const isCompleteIntentionQuery = ChatStateStore.getFlags(state.chat_id).is_complete_user_query

    if (isCompleteIntentionQuery) {
      return await this.continueIntentionQuery(state)
    }

    // 挖需
    const slotAsk = await this.getSlotAsk(state)
    if (!slotAsk) { // 挖需结束
      ChatStateStore.update(state.chat_id, {
        state: {
          is_complete_user_query: true
        }
      })

      const nextNode = await this.continueIntentionQuery(state)

      await this.sendEnergyTest(state)

      return nextNode
    }


    if (slotAsk.slotType === MoerUserSlotType.is_attend_live_stream) {
      const currentTime = await DataService.getCurrentTime(state.chat_id)
      await sleep(9000)

      if (!currentTime.is_course_week) {
        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '[询问客户下一周晚8点开课时间是否方便]',
          send_msg: {
            type: IWecomMsgType.Voice,
            duration: 9,
            voiceUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/%e6%8c%96%e9%9c%802%e9%97%ae%e4%b8%8b%e5%91%a8%e4%b8%8a%e8%af%be%e6%98%af%e5%90%a6%e5%8f%af%e4%bb%a5.silk'
          }
        })
      } else {
        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '今晚8点咱们就开始直播课了，咱们时间OK吗？可以准时到课不。',
        })
      }

    } else if (slotAsk.slotType === MoerUserSlotType.meditation_goal) {
      if (!((await ChatHistoryService.getFormatChatHistoryByChatId(state.chat_id)).includes('咱们之前了解过冥想吗'))) {
        // 如果上一条是 AI 消息，不调用 FreeTalk
        const lastMessage = await ChatHistoryService.getLastMessage(state.chat_id)
        if (lastMessage.role === 'user') {
          await ChatHistoryService.moveToEnd(state.chat_id, state.userMessage)

          await FreeTalk.invoke(state)
        }

        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '咱们之前了解过冥想吗，有具体想要解决的问题不？😊（班班贴合咱们情况提供更针对性学习体验）',
        })
      }
    } else {
      await LLMNode.invoke({
        state,
        dynamicPrompt: this.appendEnergyTestInfo(slotAsk.slotQuestion),
        referenceChatHistory: true,
        useRAG: true
      })
    }

    // 槽位计数
    const slotCount = ChatStateStore.get(state.chat_id).slotAskedCount
    slotCount[slotAsk.slotType] = slotCount[slotAsk.slotType] ? slotCount[slotAsk.slotType] + 1 : 1

    return MoerNode.IntentionQuery
  }

    /**
     * 根据客户槽位，提取要问的问题
     * @param state
     * @private
     */
    private static async getSlotAsk(state: IWorkflowState) {
      // 获取 userSlots
      // TODO: 提取槽位
      const userSlots = await this.extractUserSlots(state)
      const slotPromptMap = {
        [MoerUserSlotType.is_attend_live_stream]: '需要确认下客户上课期间晚上8点是不是OK的。例如："咱们下周一晚上8点开始老师的直播课哈。咱们时间都可以不"',
        [MoerUserSlotType.meditation_goal]: '询问客户需求了解其对冥想的了解程度，通过这次学习想收获什么。例如："对啦，咱们之前了解过冥想吗，有具体想要解决的问题不？😊（班班贴合咱们情况提供更针对性学习体验）"'
      }

      const slotCount = ChatStateStore.get(state.chat_id).slotAskedCount

      // 选取当前应该问的槽位
      const toAskedSlots = [MoerUserSlotType.is_attend_live_stream]
      for (let i = toAskedSlots.length - 1; i >= 0; i--) { // 倒序遍历，保证安全删除
        const toAskedSlot = toAskedSlots[i]
        if (slotCount[toAskedSlot] >= 1) {
          toAskedSlots.splice(i, 1)
          continue
        }

        if (userSlots && userSlots[toAskedSlot] !== undefined) { // 如果上文中已经有客户信息，不再提问
          toAskedSlots.splice(i, 1)
          continue
        }
      }

      if (toAskedSlots.length === 0) {
        return null
      }

      const toAskSlot = toAskedSlots[0]

      return {
        slotQuestion: slotPromptMap[toAskSlot],
        slotType: toAskSlot,
      }
    }

    private static async extractUserSlots(state: IWorkflowState) {
      const chat_id = state.chat_id
      const chatHistory = ChatHistoryService.formatHistoryHelper(await ChatHistoryService.getRecentConversations(state.chat_id, 3))
      const extractPrompt = await ExtractUserSlotsPrompt.format(chatHistory)

      let currentUserSlots = await ExtractUserSlots.extract(extractPrompt, ExtractUserSlotsPrompt.schema, state)
      const prevUserSlots = ChatStateStore.get(chat_id).userSlots

      if (currentUserSlots) {
        currentUserSlots = await this.mergeUserSlots(prevUserSlots, currentUserSlots)
        ChatStateStore.update(chat_id, { userSlots: currentUserSlots })
      }

      return currentUserSlots
    }

    private static async mergeUserSlots(prevUserSlots: Record<string, any>, currentUserSlots: Record<string, any>) {
    // 这里可以加一些 合并规则，不一定所有情况都去更新槽位
      return ObjectUtil.merge(prevUserSlots, currentUserSlots)
    }

    private static async continueIntentionQuery(state: IWorkflowState) {
      const isNeedEmpathy = await this.isNeedEmpathy(state)
      const nodeInvokeCount = ChatStateStore.getNodeCount(state.chat_id, IntentionQueryNode.name)

      if (isNeedEmpathy && nodeInvokeCount < 5) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: this.appendEnergyTestInfo(`你当前的具体任务是挖掘客户需求：
客户已经完成小讲堂，看过了唐宁老师有关冥想作用的介绍，为了进一步帮助客户明确冥想对于其个人价值。按照以下步骤进行沟通
带入客户角色，共情，理解客户当前的处境，让客户有安全感，产生信任。也许你可以举一些你看过学员例子或者你亲身经历，表明你是真的理解客户处境。
例如：客户和你抱怨在照顾生病的老公，一天只能睡2-3h，还常常经受婆婆的冷嘲热讽，导致内心很崩溃，不找出路可能会崩掉
回答：人在睡眠不足的时候本来就情绪差，您还需要忍受婆婆的攻击。能想到您的是有多辛苦！不过您在这个时候还能学习，也太棒了！`),
          temperature: 1,
          useRAG: true
        })

        return MoerNode.IntentionQuery
      } else {
        await LLMNode.invoke({
          state,
          dynamicPrompt: this.appendEnergyTestInfo(`基于你对冥想作用的了解，对卡点的原因重新进行解读，并解释后续课程可能在哪些方面给予帮助。
冥想核心作用是帮助练习者安静下来，内观自己念头，从而产生更多对自己认识和觉察，从而更好活出自己。当你开始做自己，手上事情也都顺了
例如：客户表达痛点是睡眠不好时
回答：其实睡眠不好很大程度因为我们身体虽然在休息，但是脑子里念头还是在乱跑，我们没有安放好这些念头，所以身体心思还是很紧绷的。当然就睡不好了。所以首先我们先通过冥想看见我们觉察到这些念头，放下他们。下周一的第一节课老师就会讲这个问题处理了。对您一定很有帮助`),
          temperature: 0.8,
          useRAG: true
        })
        return MoerNode.FreeTalk
      }
    }

    private static async sendEnergyTest(state: IWorkflowState) {
      await sleep(15 * 1000)

      if (ChatStateStore.getFlags(state.chat_id).is_delayed_send_energy_test) {
        return
      }

      await new SendEnergyTest().process({
        chatId: state.chat_id,
        userId: state.user_id,
        name: TaskName.SendEnergyTest
      })
    }

    private static async isNeedEmpathy(state: IWorkflowState) {
      const userNeed = ChatStateStore.get(state.chat_id).userSlots.meditation_goal
      const prompt = await IsNeedEmpathyPrompt.format(userNeed as string, state.userMessage)
      return await LLMXMLHelper.predictAndExtractBooleanAnswer(prompt, {
        tagName: 'decision',
        trueFlag: 'true',
        falseFlag: 'false'
      })
    }

    private static appendEnergyTestInfo(dynamicPrompt: string) {
      return `${dynamicPrompt} 
如果客户询问能量测评，可以告知客户能量测评链接为 https://jsj.top/f/W8ktas`
    }

}