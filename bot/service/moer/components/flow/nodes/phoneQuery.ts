import { <PERSON>rWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { FreeTalk } from '../../agent/free_talk'
import { RegexHelper } from '../../../../../lib/regex/regex'
import { LLMNode } from './llm'
import { MessageSender } from '../../message/message_send'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { MoerAPI } from '../../../../../model/moer_api/moer'
import logger from '../../../../../model/logger/logger'
import { DataService } from '../../../getter/getData'
import { NewCourseUser } from '../helper/newCourseUser'
import { sleep } from '../../../../../lib/schedule/schedule'

export class PhoneQuery extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 查询电话号码
    const userSlots = ChatStateStore.get(state.chat_id).userSlots
    if (userSlots && userSlots.phoneNumber) {
      return await FreeTalk.invoke(state)
    }

    // 解析回复中客户的手机号
    const phoneNumber = RegexHelper.extractPhoneNumber(state.userMessage)
    if (phoneNumber) {
      try {
        const moerUser = await MoerAPI.getUserByPhone(phoneNumber)
        const courseNo = DataService.parseCourseNo(moerUser)

        if (moerUser) {
          const notBoughtCourseBefore = await NewCourseUser.create(state.user_id, state.chat_id, courseNo, phoneNumber, moerUser.id.toString())
          await sleep(5000)

          if (notBoughtCourseBefore) {
            await MessageSender.sendById({
              user_id: state.user_id,
              chat_id: state.chat_id,
              ai_msg: '好的，收到。直接用这个手机号登录，前面发的小讲堂链接就可以观看了哈'
            })
          }

          return MoerNode.FreeTalk
        } else {
          logger.debug('查不到', phoneNumber, '对应 moerId')
          await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.NotBindPhone)
          return MoerNode.PhoneQuery
        }
      } catch (e) {
        logger.debug('查不到', phoneNumber, '对应 moerId', e)
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.NotBindPhone)
        return MoerNode.PhoneQuery
      }
    } else {
      await LLMNode.invoke({
        state,
        dynamicPrompt: `客户当前没有提供注册手机号或提供的手机号校验后不正确，需要重新询问客户购买课程的手机号，有手机号才能匹配到对应的课程并提供后续服务。
特别注意当前不知道且没有途径获取到客户的手机号，只能由客户告知, 所以必须询问客户手机号是多少，即使客户有提到"这个"之类
例如：“咱这边当时买课的手机号是多少？”“咱这边手机号没有在课程中查到，能再确认下手机号么”`,
        useRAG: true
      })

      const nodeInvokeCount = ChatStateStore.getNodeCount(state.chat_id, PhoneQuery.name)

      if (nodeInvokeCount >= 3) {
        logger.debug('查不到', phoneNumber, '对应 moerId')
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.NotBindPhone)
        return MoerNode.PhoneQuery
      } else {
        return MoerNode.PhoneQuery
      }
    }
  }
}