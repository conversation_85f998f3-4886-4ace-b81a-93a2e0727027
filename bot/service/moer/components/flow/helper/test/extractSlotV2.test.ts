import { Config } from '../../../../../../config/config'
import { ChatStateStore, ChatStatStoreManager } from '../../../../storage/chat_state_store'
import { PromptBuilder } from '../../../agent/context'
import { ChatHistoryWithRoleAndDate, chatHistoryWithRoleAndDateListToString, ExtractUserSlotsV2, UserSlot, UserSlots } from '../slotsExtract'
const chatId = '7881302016911103_1688856297674847'

describe('槽位提取', function () {
  beforeAll(async () => {
    Config.setting.langsmith.projectName = 'evaluators'
    await ChatStatStoreManager.initState(chatId)
    Config.setting.localTest = false
  })
  jest.setTimeout(200000)
  it('1.1. 槽位提取v2设计1', async () => {
    const response = await ExtractUserSlotsV2.extractUserSlotsFromChatHistory([{ role:'user', date:'2025-03-13', message:'我想给我14岁的女儿学习，她抑郁状态，你们有对应的服务么？' }, { role:'user', date:'2025-03-13', message:'她目前是无知无畏的状态' }, { role:'user', date:'2025-03-13', message:'很难沟通' }], chatId, false)
    console.dir(response, { depth:Infinity })
  })

  it('1.2. 槽位提取v2设计2', async () => {
    const response = await ExtractUserSlotsV2.extractUserSlotsFromChatHistory([{ role:'user', date:'2025-03-13', message:'我睡眠很不好，跳不动，总是乏力，那我也跳，但不敢多跳，进步慢🤦，我五年前带我姑娘儿直播跳舞，但坚持大半年，身体吃不消，就停了🤦，真想把睡眠调好了，把直播间干起来，感觉那是我老年生活的乐趣[呲牙]' }, { role:'user', date:'2025-03-13', message:'睡眠不好让我焦虑啊！' }, { role:'user', date:'2025-03-13', message:'我20岁左右曾经7天7宿不睡觉，就面瘫了，眼斜嘴歪' }], chatId, false)
    console.dir(response, { depth:Infinity })
  })
  it('1.3. 槽位提取v2设计3', async () => {
    const response = await ExtractUserSlotsV2.extractUserSlotsFromChatHistory([{ role:'user', date:'2025-03-13', message:'2' }, { role:'user', date:'2025-03-13', message:'3' }, { role:'user', date:'2025-03-13', message:'1' }], chatId, false)
    console.dir(response, { depth:Infinity })
  })

  it('2. 测试ChatHistoryWithRoleAndDateToString', () => {
    const chatHistory:ChatHistoryWithRoleAndDate[] = [{ role:'user', date:'2025-03-13', message:'我想给我14岁的女儿学习，她抑郁状态，你们有对应的服务么？' }, { role:'user', date:'2025-03-13', message:'她目前是无知无畏的状态' }]
    console.log(chatHistoryWithRoleAndDateListToString(chatHistory))
  })

  it('3. 测试槽位合并功能', async () => {
    const userSlotsA = new UserSlots([new UserSlot('人生经历', '痛点', '父母已60多岁，没有收入，需要客户和家人打工资助生活费用。'), new UserSlot('人生经历', '严重失眠经历及后果记录', '客户从初中开始长期睡眠不好，20岁时因连续7天不睡觉导致面瘫，并出现眼斜嘴歪症状；此外，她还经历长年不睡觉，全身疼痛，这可能与抑郁有关。'), ])
    const userSlotsB = new UserSlots([new UserSlot('人生经历', '痛点', '原生家庭中存在情感缺失，父亲从小被忽视，客户从小受到欺压长大。')])
    await userSlotsA.merge(userSlotsB)
    console.dir(userSlotsA.slots, { depth:Infinity })
  })
  it('4. 槽位提取秘密v2设计', async () => {
    const response = await ExtractUserSlotsV2.extractUserSlotsFromChatHistory([{ role:'user', date:'2025-03-13', message:'我想给我14岁的女儿学习，她抑郁状态，你们有对应的服务么？' }, { role:'user', date:'2025-03-13', message:'她目前是无知无畏的状态' }, { role:'user', date:'2025-03-13', message:'很难沟通' }], chatId, true)
    console.dir(response, { depth:Infinity })
  })

  it('6. 测试修复提取槽位原始语句', async () => {
    const response = await UserSlot.fromString('今天天气真好')
    console.dir(response, { depth:Infinity })
  })

  it('7. 测试修复提取槽位原始语句是其他格式', async () => {
    const response = await UserSlot.fromString('{"topic":"人生经历","description":"痛点","content":"原生家庭中存在情感缺失，父亲从小被忽视，客户从小受到欺压长大。"}')
    console.dir(response, { depth:Infinity })
  })

  it('8. 测试获得槽位信息', async() => {
    const chatState = ChatStateStore.get(chatId)
    const userSlot = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, false)
    console.log(userSlot.toString())
  })
  it('9. 测试获得槽位信息通过主题获取', async() => {
    const chatState = ChatStateStore.get(chatId)
    const userSlot = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, false)
    console.log(userSlot.getStringByTopic('痛点'))
  })
  it('10. 测试获取客户槽位提示词', async() => {
    console.log(await PromptBuilder.getCustomerPortrait(chatId))
  })
  it('11. 测试获得槽位信息', async() => {
    const userSlot = new UserSlots([])
    console.log(userSlot.toString())
  })

  it('12. 测试槽位遗忘功能', async() => {
    const chatState = ChatStateStore.get(chatId)
    const userSlot = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, false)
    console.log('start')
    await userSlot.forget()
    console.log(userSlot.slots)
    console.log(userSlot.toString())
  })
})