import { MetaActionComponent } from '../meta_action_component'
import { IActionInfo } from './post_action'
import { DataService } from '../../../getter/getData'
import { FreeThink } from '../../agent/free_think'


export class AfterPaid extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return Promise.resolve(isPaid)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getAdditionInfo(chatId: string): Promise<string> {
    return Promise.resolve('\n注意：客户当前已购买冥想系统班')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(FreeThink.metaActionAfterPaid)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(FreeThink.thinkPromptAfterPaid)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }

}
