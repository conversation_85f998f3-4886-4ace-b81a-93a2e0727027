import { CourseCompletionCache } from './course_completion_cache'
import { DataService } from '../../getter/getData'

// Mock DataService
jest.mock('../../getter/getData')
const mockedDataService = DataService as jest.Mocked<typeof DataService>

describe('CourseCompletionCache', () => {
  const testChatId = 'test_chat_123'
  
  beforeEach(() => {
    // 清除缓存
    CourseCompletionCache.clearCache(testChatId)
    
    // 重置 mock
    jest.clearAllMocks()
    
    // 设置默认的 mock 返回值
    mockedDataService.getCurrentTime.mockResolvedValue({
      is_course_week: true,
      day: 1,
      time: '19:00:00'
    })
    
    mockedDataService.isWithinClassTime.mockResolvedValue(false)
  })

  describe('isCompletedCourse', () => {
    it('应该在首次调用时从 API 获取数据', async () => {
      // 设置 mock 返回值
      mockedDataService.isCompletedCourse.mockResolvedValue(true)
      
      // 调用缓存方法
      const result = await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1 })
      
      // 验证结果
      expect(result).toBe(true)
      expect(mockedDataService.isCompletedCourse).toHaveBeenCalledTimes(1)
      expect(mockedDataService.isCompletedCourse).toHaveBeenCalledWith(testChatId, { day: 1 })
    })

    it('应该在缓存有效期内返回缓存数据', async () => {
      // 设置 mock 返回值
      mockedDataService.isCompletedCourse.mockResolvedValue(true)
      
      // 第一次调用
      const result1 = await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1 })
      
      // 第二次调用（应该使用缓存）
      const result2 = await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1 })
      
      // 验证结果
      expect(result1).toBe(true)
      expect(result2).toBe(true)
      expect(mockedDataService.isCompletedCourse).toHaveBeenCalledTimes(1) // 只调用一次 API
    })

    it('应该区分直播和录播课程', async () => {
      // 设置 mock 返回值
      mockedDataService.isCompletedCourse
        .mockResolvedValueOnce(true)  // 直播课程
        .mockResolvedValueOnce(false) // 录播课程
      
      // 调用不同类型的课程
      const liveResult = await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1 })
      const recordingResult = await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1, is_recording: true })
      
      // 验证结果
      expect(liveResult).toBe(true)
      expect(recordingResult).toBe(false)
      expect(mockedDataService.isCompletedCourse).toHaveBeenCalledTimes(2)
    })
  })

  describe('getCourseCompletionInfo', () => {
    it('应该返回正确格式的课程完成信息', async () => {
      // 设置 mock 返回值 - 模拟完成了前两个课程
      mockedDataService.isCompletedCourse
        .mockResolvedValueOnce(true)  // 小课堂 - 直播
        .mockResolvedValueOnce(false) // 小课堂 - 录播
        .mockResolvedValueOnce(true)  // 第一课 - 直播
        .mockResolvedValueOnce(false) // 第一课 - 录播
        .mockResolvedValueOnce(false) // 第二课 - 直播
        .mockResolvedValueOnce(false) // 第二课 - 录播
        .mockResolvedValueOnce(false) // 第三课 - 直播
        .mockResolvedValueOnce(false) // 第三课 - 录播
        .mockResolvedValueOnce(false) // 第四课 - 直播
        .mockResolvedValueOnce(false) // 第四课 - 录播
      
      const result = await CourseCompletionCache.getCourseCompletionInfo(testChatId)
      
      // 验证结果包含预期的内容
      expect(result).toContain('## 客户行为')
      expect(result).toContain('小课堂海浪冥想（已完成）')
      expect(result).toContain('第一课情绪减压（已完成）')
      expect(result).toContain('第二课财富唤醒（未完成）')
      expect(result).toContain('完成了部分课程')
    })

    it('应该正确处理课程未开始的情况', async () => {
      // 设置当前时间为课程开始前
      mockedDataService.getCurrentTime.mockResolvedValue({
        is_course_week: true,
        day: 0,
        time: '19:00:00'
      })
      
      // 设置所有课程都未完成
      mockedDataService.isCompletedCourse.mockResolvedValue(false)
      
      const result = await CourseCompletionCache.getCourseCompletionInfo(testChatId)
      
      // 验证结果
      expect(result).toContain('第一课情绪减压（直播未开始）')
      expect(result).toContain('第二课财富唤醒（直播未开始）')
      expect(result).toContain('第三课红靴子（直播未开始）')
    })
  })

  describe('缓存管理', () => {
    it('应该能够清除指定聊天的缓存', async () => {
      // 设置 mock 返回值
      mockedDataService.isCompletedCourse.mockResolvedValue(true)
      
      // 第一次调用建立缓存
      await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1 })
      
      // 清除缓存
      CourseCompletionCache.clearCache(testChatId)
      
      // 再次调用应该重新从 API 获取
      await CourseCompletionCache.isCompletedCourse(testChatId, { day: 1 })
      
      // 验证 API 被调用了两次
      expect(mockedDataService.isCompletedCourse).toHaveBeenCalledTimes(2)
    })

    it('应该返回正确的缓存统计信息', () => {
      const stats = CourseCompletionCache.getCacheStats()
      
      expect(stats).toHaveProperty('size')
      expect(stats).toHaveProperty('maxSize')
      expect(typeof stats.size).toBe('number')
      expect(typeof stats.maxSize).toBe('number')
    })
  })

  describe('预热缓存', () => {
    it('应该能够预热缓存', async () => {
      // 设置 mock 返回值
      mockedDataService.isCompletedCourse.mockResolvedValue(false)
      
      // 预热缓存
      await CourseCompletionCache.warmupCache(testChatId)
      
      // 验证 API 被调用了（预热过程中会调用 getCourseCompletionInfo）
      expect(mockedDataService.isCompletedCourse).toHaveBeenCalled()
    })
  })
})
