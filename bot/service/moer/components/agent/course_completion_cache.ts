import { LRUCache } from 'lru-cache'
import { DataService } from '../../getter/getData'
import logger from '../../../../model/logger/logger'
import { isScheduleTimeAfter, isScheduleTimeBefore } from '../schedule/creat_schedule_task'

interface CourseInfo {
  day: number
  is_recording?: boolean
}

interface CourseCompletionStatus {
  isCompleted: boolean
  timestamp: number
}

interface CourseCompletionInfo {
  courses: {
    [key: string]: CourseCompletionStatus
  }
  lastFullUpdateTime: number
}

/**
 * 课程完成状态缓存
 * 用于优化 getCourseCompletionInfo 函数的性能
 */
export class CourseCompletionCache {
  // 缓存过期时间（毫秒）
  private static readonly CACHE_TTL_DEFAULT = 5 * 60 * 1000 // 5分钟
  private static readonly CACHE_TTL_DURING_CLASS = 2 * 60 * 1000 // 2分钟（上课期间）
  private static readonly CACHE_TTL_AFTER_CLASS = 30 * 60 * 1000 // 30分钟（课后）
  private static readonly FULL_UPDATE_INTERVAL = 30 * 60 * 1000 // 30分钟做一次全量更新

  // 使用 LRU 缓存存储课程完成状态
  private static cache = new LRUCache<string, CourseCompletionInfo>({
    max: 1000, // 最多缓存1000个用户
    ttl: CourseCompletionCache.CACHE_TTL_DEFAULT,
  })

  /**
   * 获取课程完成状态
   * @param chatId 聊天ID
   * @param course 课程信息
   * @returns 课程是否完成
   */
  public static async isCompletedCourse(chatId: string, course: CourseInfo): Promise<boolean> {
    const cacheKey = this.getCacheKey(chatId, course)
    const cacheInfo = this.cache.get(chatId)
    const now = Date.now()

    // 如果缓存存在且未过期
    if (
      cacheInfo &&
      cacheInfo.courses[cacheKey] &&
      now - cacheInfo.courses[cacheKey].timestamp <
        (await this.getCacheTTL(chatId))
    ) {
      return cacheInfo.courses[cacheKey].isCompleted
    }

    // 缓存不存在或已过期，调用API获取最新状态
    const isCompleted = await DataService.isCompletedCourse(chatId, course)

    // 更新缓存
    this.updateCourseStatus(chatId, course, isCompleted)

    return isCompleted
  }

  /**
   * 更新课程状态缓存
   * @param chatId 聊天ID
   * @param course 课程信息
   * @param isCompleted 是否完成
   */
  private static updateCourseStatus(chatId: string, course: CourseInfo, isCompleted: boolean): void {
    const cacheKey = this.getCacheKey(chatId, course)
    const now = Date.now()

    // 获取或创建缓存对象
    let cacheInfo = this.cache.get(chatId)
    if (!cacheInfo) {
      cacheInfo = {
        courses: {},
        lastFullUpdateTime: now
      }
    }

    // 更新课程状态
    cacheInfo.courses[cacheKey] = {
      isCompleted,
      timestamp: now
    }

    // 更新缓存
    this.cache.set(chatId, cacheInfo)
  }

  /**
   * 获取缓存键
   * @param chatId 聊天ID
   * @param course 课程信息
   * @returns 缓存键
   */
  private static getCacheKey(chatId: string, course: CourseInfo): string {
    return `${course.day}_${course.is_recording ? 'recording' : 'live'}`
  }

  /**
   * 获取缓存TTL
   * @param chatId 聊天ID
   * @returns 缓存TTL（毫秒）
   */
  private static async getCacheTTL(chatId: string): Promise<number> {
    try {
      const currentTime = await DataService.getCurrentTime(chatId)

      // 如果不在课程周，使用较长的缓存时间
      if (!currentTime.is_course_week) {
        return this.CACHE_TTL_AFTER_CLASS
      }

      // 判断是否在上课时间
      const isInClass = await DataService.isWithinClassTime(currentTime)
      if (isInClass) {
        return this.CACHE_TTL_DURING_CLASS
      }

      // 判断是否已经上完课
      const dayEndTimeMap = {
        1: '21:35:00', // 星期一
        2: '21:05:00', // 星期二
        3: '21:40:00', // 星期三
        4: '22:05:00'  // 星期四
      }

      if (currentTime.day > 0 && currentTime.day <= 4) {
        const isAfterCourse = isScheduleTimeAfter(
          currentTime,
          { is_course_week: true, day: currentTime.day, time: dayEndTimeMap[currentTime.day] }
        )

        if (isAfterCourse) {
          return this.CACHE_TTL_AFTER_CLASS
        }
      }

      // 默认缓存时间
      return this.CACHE_TTL_DEFAULT
    } catch (error) {
      logger.error('获取缓存TTL失败', error)
      return this.CACHE_TTL_DEFAULT
    }
  }

  /**
   * 清除指定聊天的缓存
   * @param chatId 聊天ID
   */
  public static clearCache(chatId: string): void {
    this.cache.delete(chatId)
  }

  /**
   * 清除指定课程的缓存
   * @param chatId 聊天ID
   * @param course 课程信息
   */
  public static clearCourseCache(chatId: string, course: CourseInfo): void {
    const cacheInfo = this.cache.get(chatId)
    if (cacheInfo) {
      const cacheKey = this.getCacheKey(chatId, course)
      delete cacheInfo.courses[cacheKey]
      this.cache.set(chatId, cacheInfo)
    }
  }

  /**
   * 预热缓存 - 在系统启动或低峰期预先加载常用数据
   * @param chatId 聊天ID
   */
  public static async warmupCache(chatId: string): Promise<void> {
    try {
      logger.trace(`预热缓存开始: ${chatId}`)
      await this.getCourseCompletionInfo(chatId)
      logger.trace(`预热缓存完成: ${chatId}`)
    } catch (error) {
      logger.error(`预热缓存失败: ${chatId}`, error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  public static getCacheStats(): { size: number; maxSize: number } {
    return {
      size: this.cache.size,
      maxSize: this.cache.max
    }
  }

  /**
   * 获取所有课程的完成状态
   * @param chatId 聊天ID
   * @returns 课程完成状态的字符串表示
   */
  public static async getCourseCompletionInfo(chatId: string): Promise<string> {
    const currentTime = await DataService.getCurrentTime(chatId)
    let courseCompletion = '## 客户行为'
    let courseCompletionCount = 0
    const now = Date.now()

    // 获取缓存信息
    let cacheInfo = this.cache.get(chatId)
    const needFullUpdate = !cacheInfo || (now - cacheInfo.lastFullUpdateTime > this.FULL_UPDATE_INTERVAL)

    // 如果需要全量更新或缓存不存在，初始化缓存
    if (needFullUpdate) {
      if (!cacheInfo) {
        cacheInfo = {
          courses: {},
          lastFullUpdateTime: now
        }
      }
      cacheInfo.lastFullUpdateTime = now
    }

    const courses = [
      { day: 0, label: '小课堂海浪冥想' },
      { day: 1, label: '第一课情绪减压' },
      { day: 2, label: '第二课财富唤醒' },
      { day: 3, label: '第三课红靴子' },
      { day: 4, label: '加播课蓝鹰预演' }
    ]

    for (const course of courses) {
      // 检查是否需要更新此课程的状态
      const liveCacheKey = this.getCacheKey(chatId, { day: course.day })
      const recordingCacheKey = this.getCacheKey(chatId, { day: course.day, is_recording: true })

      let isLiveCompleted = false
      let isRecordingCompleted = false

      // 检查直播课程状态
      const liveCacheEntry = cacheInfo?.courses[liveCacheKey]
      if (needFullUpdate || !liveCacheEntry || (now - liveCacheEntry.timestamp > await this.getCacheTTL(chatId))) {
        isLiveCompleted = await DataService.isCompletedCourse(chatId, { day: course.day })
        cacheInfo.courses[liveCacheKey] = { isCompleted: isLiveCompleted, timestamp: now }
      } else {
        isLiveCompleted = liveCacheEntry.isCompleted
      }

      // 检查录播课程状态
      const recordingCacheEntry = cacheInfo.courses[recordingCacheKey]
      if (needFullUpdate || !recordingCacheEntry || (now - recordingCacheEntry.timestamp > await this.getCacheTTL(chatId))) {
        isRecordingCompleted = await DataService.isCompletedCourse(chatId, { day: course.day, is_recording: true })
        cacheInfo.courses[recordingCacheKey] = { isCompleted: isRecordingCompleted, timestamp: now }
      } else {
        isRecordingCompleted = recordingCacheEntry.isCompleted
      }

      // 更新缓存
      this.cache.set(chatId, cacheInfo)

      // 构建返回信息
      if (isLiveCompleted || isRecordingCompleted) {
        courseCompletionCount++
        courseCompletion += `\n- ${course.label}（已完成）`
      } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: course.day, time: '20:00:00' })) {
        courseCompletion += `\n- ${course.label}（直播未开始）`
      } else {
        courseCompletion += `\n- ${course.label}（未完成）`
      }
    }

    const completionMessages = [
      '未完成任何课程',
      '完成了小部分课程',
      '完成了部分课程',
      '完成了大部分课程',
      '完成了大部分课程',
      '完成了所有课程'
    ]

    courseCompletion += `\n- ${completionMessages[courseCompletionCount]}`

    return courseCompletion
  }
}
