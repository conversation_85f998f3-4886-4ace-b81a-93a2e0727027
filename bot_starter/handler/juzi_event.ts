import { z } from 'zod'
import { SendWelcomeMessage } from '../../bot/service/moer/components/flow/schedule/task/sendWelcomeMessage'
import { getChatId } from '../../bot/config/chat_id'
import { TaskName } from '../../bot/service/moer/components/flow/schedule/type'
import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { DataService } from '../../bot/service/moer/getter/getData'
import { JuziAPI } from '../../bot/lib/juzi/api'
import { Config } from '../../bot/config/config'
import { catchError } from '../../bot/lib/error/catchError'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { LLM } from '../../bot/lib/ai/llm/LLM'

// 定义 FollowUser 接口
interface FollowUser {
    wecomUserId: string
}

// 定义 ImInfo 接口
interface ImInfo {
    externalUserId: string
    followUser: FollowUser
}

// 定义 BotInfo 接口
interface BotInfo {
    botId: string
    imBotId: string
    name: string
    avatar: string
}

// 定义主接口 Contact
interface FriendAcceptedEvent {
    imContactId: string
    name: string
    avatar: string
    gender: number
    createTimestamp: number
    imInfo: ImInfo
    botInfo: BotInfo
}

export class JuziEvent {
  public static async handle(data: any) {
    // 使用 zod 校验
    if (this.isFriendAcceptedEvent(data)) {
      return await this.handleFriendAcceptedEvent(data as FriendAcceptedEvent)
    }

    console.log('event:', JSON.stringify(data, null, 4))
  }

  public static isFriendAcceptedEvent(data: any) {
    const schema = z.object({
      imContactId: z.string(),
      name: z.string(),
      avatar: z.string().url(),
      gender: z.number(),
      createTimestamp: z.number(),
      imInfo: z.object({
        externalUserId: z.string(),
        followUser: z.object({
          wecomUserId: z.string()
        })
      }),
      botInfo: z.object({
        botId: z.string(),
        imBotId: z.string(),
        name: z.string(),
        avatar: z.string().url()
      })
    })

    const result = schema.safeParse(data)

    return result.success
  }

  public static async handleFriendAcceptedEvent(data: FriendAcceptedEvent) {
    // 只处理一次加好友事件
    const userId = data.imContactId
    const chatId = getChatId(userId)

    // 确保状态正确初始化
    await ChatStatStoreManager.initState(chatId)

    const isFriendAccepted = ChatStateStore.getFlags(chatId).is_friend_accepted
    if (isFriendAccepted) {
      return
    }

    ChatStateStore.update(chatId, {
      state: {
        is_friend_accepted: true
      }
    })

    // 这里直接调用一下 getCustomerInfo，防止被客户删除掉获取不到客户信息
    await catchError(JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId))

    // 分析头像并更新客户画像
    await this.analyzeAvatarAndUpdateProfile(data, chatId, userId)

    await new SendWelcomeMessage().process({ chatId, userId: userId, name: TaskName.SendWelcomeMessage })

    await DataService.saveChat(chatId, userId)

    // 拿这里的姓名去更新一下 微信名
    const chat = await ChatDB.getById(chatId)
    if (chat && chat.contact.wx_name !== data.name) {
      await ChatDB.updateContact(chatId, userId, data.name)
    }
  }

  /**
   * 分析头像并更新客户画像
   */
  public static async analyzeAvatarAndUpdateProfile(data: FriendAcceptedEvent, chatId: string, userId: string) {
    try {
      // 头像分析prompt
      const avatarAnalysisPrompt = `你是一名专业的头像分析师，请帮我对客户的头像进行分析。

## 分析方向：
1. 如果头像是真人图片，请以【真人照片】开头，然后判断出客户的性别和大概的年龄区间。另外大概描述一下人物形象和风格。
2. 如果头像是二次元/动漫风格的图片则以【动漫头像】开头，然后描述一下头像风格。
3. 如果头像是风景图片则回复【头像为风景照，不进行分析】。
4. 其他类型的头像图片则回复【其他类型头像，不进行分析】`

      // 使用LLM分析头像
      const avatarAnalysis = await new LLM({ max_tokens: 300 }).imageChat(data.avatar, avatarAnalysisPrompt)

      // 性别映射
      const genderMap: { [key: number]: string } = {
        0: '未知',
        1: '男',
        2: '女'
      }

      // 更新客户画像
      const userSlotsUpdate = {
        avatar_analysis: avatarAnalysis,
        gender: genderMap[data.gender] || '未知',
        wx_nickname: data.name
      }

      ChatStateStore.update(chatId, {
        userSlots: userSlotsUpdate
      })

      // 立即保存到数据库
      await DataService.saveChat(chatId, userId)

      console.log(`[JuziEvent] 头像分析完成并已保存 ${chatId}:`, {
        avatar_analysis: avatarAnalysis,
        gender: userSlotsUpdate.gender,
        wx_nickname: data.name
      })

    } catch (error) {
      console.error(`[JuziEvent] 头像分析失败 ${chatId}:`, error)
      // 即使头像分析失败，也要保存基本的性别和昵称信息
      const genderMap: { [key: number]: string } = {
        0: '未知',
        1: '男',
        2: '女'
      }

      ChatStateStore.update(chatId, {
        userSlots: {
          gender: genderMap[data.gender] || '未知',
          wx_nickname: data.name
        }
      })

      // 保存基本信息到数据库
      await DataService.saveChat(chatId, userId)
    }
  }
}